import type { Service, ApiError } from '../types'

const apiBaseUrl = import.meta.env.VITE_API_BASE_URL

if (!apiBaseUrl) {
  throw new Error('Missing VITE_API_BASE_URL environment variable')
}

// Ensure the URL doesn't have a trailing slash
const API_BASE_URL = apiBaseUrl.replace(/\/$/, '')

class ApiService {
  private accessToken: string | null = null
  private tokenProvider: (() => Promise<string | null>) | null = null

  setAccessToken(token: string | null) {
    this.accessToken = token
    console.log('API Service: Access token updated:', token ? 'Token set' : 'Token cleared')
  }

  setTokenProvider(provider: (() => Promise<string | null>) | null) {
    this.tokenProvider = provider
    console.log('API Service: Token provider updated:', provider ? 'Provider set' : 'Provider cleared')
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    let token = this.accessToken

    // If we have a token provider (Clerk), get fresh token
    if (this.tokenProvider) {
      try {
        token = await this.tokenProvider()
        console.log('API Service: Got fresh token from provider:', token ? 'Token available' : 'No token')
      } catch (error) {
        console.error('API Service: Error getting token from provider:', error)
      }
    }

    if (!token) {
      console.error('API Service: No authentication token available when trying to make request')
      throw new Error('No authentication token available')
    }

    console.log('API Service: Using token for request')
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    }
  }

  private async handleResponse<T>(response: Response): Promise<T> {
    if (!response.ok) {
      const error: ApiError = {
        message: `HTTP ${response.status}: ${response.statusText}`,
        status: response.status,
      }
      
      try {
        const errorData = await response.json()
        error.message = errorData.message || error.message
      } catch {
        // Use default error message if JSON parsing fails
      }
      
      throw error
    }

    return response.json()
  }

  async getServices(): Promise<Service[]> {
    const headers = await this.getAuthHeaders()
    
    const response = await fetch(`${API_BASE_URL}/dashboard/services`, {
      method: 'GET',
      headers,
    })

    return this.handleResponse<Service[]>(response)
  }

  async getService(id: string): Promise<Service> {
    const headers = await this.getAuthHeaders()
    
    const response = await fetch(`${API_BASE_URL}/v1/services/${id}`, {
      method: 'GET',
      headers,
    })

    return this.handleResponse<Service>(response)
  }
}

export const apiService = new ApiService()
