import { User } from '@auth0/auth0-react';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://api.appio.so';

export interface Service {
  id: string;
  name: string;
  status: string;
  url?: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

class ApiService {
  private getAuthHeaders(accessToken: string): HeadersInit {
    return {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${accessToken}`,
    };
  }

  async getDashboardServices(accessToken: string): Promise<Service[]> {
    try {
      const response = await fetch(`${API_BASE_URL}/dashboard/services`, {
        method: 'GET',
        headers: this.getAuthHeaders(accessToken),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ApiResponse<Service[]> = await response.json();
      
      if (!result.success) {
        throw new Error(result.message || 'Failed to fetch services');
      }

      return result.data;
    } catch (error) {
      console.error('Error fetching dashboard services:', error);
      throw error;
    }
  }
}

export const apiService = new ApiService();
