import React, { useEffect } from 'react'
import { <PERSON><PERSON><PERSON><PERSON>, useAuth as useC<PERSON>kAuth, useUser, useClerk, SignIn, SignUp } from '@clerk/clerk-react'
import { apiService } from '../api'
import type { AuthProvider, AuthState, AuthActions } from '../../types/auth'
import type { User } from '../../types'

const clerkPublishableKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY

if (!clerkPublishableKey) {
  throw new Error('Missing VITE_CLERK_PUBLISHABLE_KEY environment variable')
}

const ClerkWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ClerkProvider publishableKey={clerkPublishableKey}>
      {children}
    </ClerkProvider>
  )
}

const useClerkAuthState = (): AuthState & AuthActions => {
  const { isSignedIn, isLoaded, getToken, signOut: clerkSignOut } = useClerkAuth()
  const { user: clerkUser } = useUser()
  const clerk = useClerk()

  // Set up token provider for API service when auth state changes
  useEffect(() => {
    if (isSignedIn && isLoaded) {
      console.log('Clerk: Setting up token provider for API service')
      // Set up a token provider that gets fresh tokens on each API call
      apiService.setTokenProvider(async () => {
        try {
          const token = await getToken()
          console.log('Clerk: Fresh token retrieved for API call:', token ? 'Token available' : 'No token')
          return token
        } catch (error) {
          console.error('Clerk: Error getting fresh token:', error)
          return null
        }
      })
    } else {
      console.log('Clerk: User not signed in or not loaded, clearing token provider')
      apiService.setTokenProvider(null)
      apiService.setAccessToken(null)
    }
  }, [isSignedIn, isLoaded, getToken])

  // Enhanced auth state logging
  useEffect(() => {
    if (isLoaded) {
      if (!isSignedIn) {
        console.log('Clerk Auth state: No user signed in')
      } else if (clerkUser) {
        console.log('Clerk Auth state: User authenticated', {
          userId: clerkUser.id,
          email: clerkUser.primaryEmailAddress?.emailAddress,
          isSignedIn,
          isLoaded
        })
      }
    }
  }, [isLoaded, isSignedIn, clerkUser])

  const user: User | null = clerkUser ? {
    id: clerkUser.id,
    email: clerkUser.primaryEmailAddress?.emailAddress || '',
    created_at: clerkUser.createdAt?.toISOString() || '',
  } : null

  const signUp = async () => {
    // Open Clerk's sign up modal
    clerk.openSignUp()
  }

  const signIn = async () => {
    // Open Clerk's sign in modal
    clerk.openSignIn()
  }

  const signOut = async () => {
    try {
      await clerkSignOut()
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  return {
    user,
    loading: !isLoaded,
    isLoggedIn: !!isSignedIn && !!clerkUser,
    accessToken: null, // Clerk manages tokens internally
    signIn,
    signUp,
    signOut,
  }
}

export const clerkAuthProvider: AuthProvider = {
  Provider: ClerkWrapper,
  useAuth: useClerkAuthState,
  setApiToken: (token: string | null) => {
    apiService.setAccessToken(token)
  }
}

// Export Clerk components for use in UI
export { SignIn as ClerkSignIn, SignUp as ClerkSignUp }
